package net.aethor.handlers;

import net.aethor.config.ModConfig;
import net.aethor.features.AbstractDistanceFeature;
import net.aethor.features.FriendlyMobSoundFeature;
import net.aethor.features.HostileMobSoundFeature;
import net.aethor.features.MusicSoundFeature;
import net.aethor.features.ZoomFeature;
import net.minecraft.client.MinecraftClient;

import java.util.ArrayList;
import java.util.List;

/**
 * Handler chịu trách nhiệm xử lý tất cả input từ bàn phím
 * Quản lý và điều phối các features
 */
public class KeyInputHandler {

    private final List<AbstractDistanceFeature> features;
    private ZoomFeature zoomFeature;
    private HostileMobSoundFeature hostileMobSoundFeature;
    private FriendlyMobSoundFeature friendlyMobSoundFeature;
    private MusicSoundFeature musicSoundFeature;
    private static KeyInputHandler instance;

    public KeyInputHandler() {
        this.features = new ArrayList<>();
    }

    /**
     * Singleton pattern để đảm bảo chỉ có một instance
     */
    public static KeyInputHandler getInstance() {
        if (instance == null) {
            instance = new KeyInputHandler();
        }
        return instance;
    }

    /**
     * Generic method để đăng ký feature với validation và logging
     */
    private <T> void registerFeatureInternal(T feature, String featureType, Runnable registrationAction) {
        if (feature != null) {
            // Check if feature is enabled using switch expression
            boolean isEnabled = switch (feature) {
                case AbstractDistanceFeature df -> df.isEnabled();
                case ZoomFeature zf -> zf.isEnabled();
                case HostileMobSoundFeature hmsf -> hmsf.isEnabled();
                case FriendlyMobSoundFeature fmsf -> fmsf.isEnabled();
                case MusicSoundFeature musf -> musf.isEnabled();
                default -> true;
            };

            if (isEnabled) {
                registrationAction.run();

                if (ModConfig.DEBUG_MODE) {
                    String featureName = switch (feature) {
                        case AbstractDistanceFeature df -> df.getFeatureName();
                        case ZoomFeature zf -> zf.getFeatureName();
                        case HostileMobSoundFeature hmsf -> hmsf.getFeatureName();
                        case FriendlyMobSoundFeature fmsf -> fmsf.getFeatureName();
                        case MusicSoundFeature musf -> musf.getFeatureName();
                        default -> "Unknown";
                    };
                    System.out.println("[" + ModConfig.MOD_NAME + "] Registered " + featureType + ": " + featureName);
                }
            }
        }
    }

    /**
     * Đăng ký một distance feature mới vào handler
     */
    public void registerFeature(AbstractDistanceFeature feature) {
        registerFeatureInternal(feature, "feature", () -> features.add(feature));
    }

    /**
     * Đăng ký zoom feature
     */
    public void registerZoomFeature(ZoomFeature feature) {
        registerFeatureInternal(feature, "zoom feature", () -> this.zoomFeature = feature);
    }

    /**
     * Đăng ký hostile mob sound feature
     */
    public void registerHostileMobSoundFeature(HostileMobSoundFeature feature) {
        registerFeatureInternal(feature, "hostile mob sound feature", () -> this.hostileMobSoundFeature = feature);
    }

    /**
     * Đăng ký friendly mob sound feature
     */
    public void registerFriendlyMobSoundFeature(FriendlyMobSoundFeature feature) {
        registerFeatureInternal(feature, "friendly mob sound feature", () -> this.friendlyMobSoundFeature = feature);
    }

    /**
     * Đăng ký music sound feature
     */
    public void registerMusicSoundFeature(MusicSoundFeature feature) {
        registerFeatureInternal(feature, "music sound feature", () -> this.musicSoundFeature = feature);
    }

    /**
     * Xử lý tất cả input từ các features đã đăng ký
     * Method này được gọi mỗi client tick
     */
    public void handleAllInputs(MinecraftClient client) {
        if (client.player == null) return;

        // Duyệt qua tất cả distance features và xử lý input
        for (AbstractDistanceFeature feature : features) {
            if (feature.isEnabled()) {
                try {
                    feature.handleInput(client);
                } catch (Exception e) {
                    // Log error nhưng không crash game
                    System.err.println("[" + ModConfig.MOD_NAME + "] Error handling input for feature '" +
                            feature.getFeatureName() + "': " + e.getMessage());
                    if (ModConfig.DEBUG_MODE) {
                        System.err.println("[" + ModConfig.MOD_NAME + "] Stack trace: " + e.getClass().getSimpleName() + " at " + e.getStackTrace()[0]);
                    }
                }
            }
        }

        // Xử lý zoom feature
        if (zoomFeature != null && zoomFeature.isEnabled()) {
            try {
                zoomFeature.handleInput(client);
            } catch (Exception e) {
                System.err.println("[" + ModConfig.MOD_NAME + "] Error handling input for zoom feature: " + e.getMessage());
                if (ModConfig.DEBUG_MODE) {
                    System.err.println("[" + ModConfig.MOD_NAME + "] Stack trace: " + e.getClass().getSimpleName() + " at " + e.getStackTrace()[0]);
                }
            }
        }

        // Xử lý hostile mob sound feature
        if (hostileMobSoundFeature != null && hostileMobSoundFeature.isEnabled()) {
            try {
                hostileMobSoundFeature.handleInput(client);
            } catch (Exception e) {
                System.err.println("[" + ModConfig.MOD_NAME + "] Error handling input for hostile mob sound feature: " + e.getMessage());
                if (ModConfig.DEBUG_MODE) {
                    System.err.println("[" + ModConfig.MOD_NAME + "] Stack trace: " + e.getClass().getSimpleName() + " at " + e.getStackTrace()[0]);
                }
            }
        }

        // Xử lý friendly mob sound feature
        if (friendlyMobSoundFeature != null && friendlyMobSoundFeature.isEnabled()) {
            try {
                friendlyMobSoundFeature.handleInput(client);
            } catch (Exception e) {
                System.err.println("[" + ModConfig.MOD_NAME + "] Error handling input for friendly mob sound feature: " + e.getMessage());
                if (ModConfig.DEBUG_MODE) {
                    System.err.println("[" + ModConfig.MOD_NAME + "] Stack trace: " + e.getClass().getSimpleName() + " at " + e.getStackTrace()[0]);
                }
            }
        }

        // Xử lý music sound feature
        if (musicSoundFeature != null && musicSoundFeature.isEnabled()) {
            try {
                musicSoundFeature.handleInput(client);
            } catch (Exception e) {
                System.err.println("[" + ModConfig.MOD_NAME + "] Error handling input for music sound feature: " + e.getMessage());
                if (ModConfig.DEBUG_MODE) {
                    System.err.println("[" + ModConfig.MOD_NAME + "] Stack trace: " + e.getClass().getSimpleName() + " at " + e.getStackTrace()[0]);
                }
            }
        }
    }



    /**
     * Đếm số lượng features được enable
     */
    public int getEnabledFeatureCount() {
        int count = (int) features.stream()
                .filter(AbstractDistanceFeature::isEnabled)
                .count();

        // Thêm zoom feature nếu enabled
        if (zoomFeature != null && zoomFeature.isEnabled()) {
            count++;
        }

        // Thêm hostile mob sound feature nếu enabled
        if (hostileMobSoundFeature != null && hostileMobSoundFeature.isEnabled()) {
            count++;
        }

        // Thêm friendly mob sound feature nếu enabled
        if (friendlyMobSoundFeature != null && friendlyMobSoundFeature.isEnabled()) {
            count++;
        }

        // Thêm music sound feature nếu enabled
        if (musicSoundFeature != null && musicSoundFeature.isEnabled()) {
            count++;
        }

        return count;
    }

    /**
     * Clear tất cả features (dùng cho cleanup khi mod shutdown)
     * Reset tất cả features về trạng thái ban đầu và giải phóng resources
     * Method này được gọi trong ClientLifecycleEvents.CLIENT_STOPPING
     * tại QuickSettingKeyMod.registerShutdownEvent()
     */
    @SuppressWarnings("unused") // Used in shutdown event lambda
    public void clearAllFeatures() {
        if (ModConfig.DEBUG_MODE) {
            System.out.println("[" + ModConfig.MOD_NAME + "] Clearing all features");
        }
        features.clear();

        // Reset zoom nếu có
        if (zoomFeature != null) {
            zoomFeature.resetZoom(MinecraftClient.getInstance());
            zoomFeature = null;
        }

        // Reset hostile mob sound nếu có
        if (hostileMobSoundFeature != null) {
            hostileMobSoundFeature.resetSound(MinecraftClient.getInstance());
            hostileMobSoundFeature = null;
        }

        // Reset friendly mob sound nếu có
        if (friendlyMobSoundFeature != null) {
            friendlyMobSoundFeature.resetSound(MinecraftClient.getInstance());
            friendlyMobSoundFeature = null;
        }

        // Reset music sound nếu có
        if (musicSoundFeature != null) {
            musicSoundFeature.resetSound(MinecraftClient.getInstance());
            musicSoundFeature = null;
        }
    }

    /**
     * Validate tất cả features đã đăng ký
     */
    public boolean validateFeatures() {
        // Validate distance features
        for (AbstractDistanceFeature feature : features) {
            if (feature.getIncreaseKey() == null || feature.getDecreaseKey() == null) {
                System.err.println("[" + ModConfig.MOD_NAME + "] Invalid feature: " + feature.getFeatureName());
                return false;
            }
        }

        // Validate zoom feature
        if (zoomFeature != null && zoomFeature.getToggleZoomKey() == null) {
            System.err.println("[" + ModConfig.MOD_NAME + "] Invalid zoom feature: " + zoomFeature.getFeatureName());
            return false;
        }

        // Validate hostile mob sound feature
        if (hostileMobSoundFeature != null && hostileMobSoundFeature.getToggleKey() == null) {
            System.err.println("[" + ModConfig.MOD_NAME + "] Invalid hostile mob sound feature: " + hostileMobSoundFeature.getFeatureName());
            return false;
        }

        // Validate friendly mob sound feature
        if (friendlyMobSoundFeature != null && friendlyMobSoundFeature.getToggleKey() == null) {
            System.err.println("[" + ModConfig.MOD_NAME + "] Invalid friendly mob sound feature: " + friendlyMobSoundFeature.getFeatureName());
            return false;
        }

        // Validate music sound feature
        if (musicSoundFeature != null && musicSoundFeature.getToggleKey() == null) {
            System.err.println("[" + ModConfig.MOD_NAME + "] Invalid music sound feature: " + musicSoundFeature.getFeatureName());
            return false;
        }

        return true;
    }

}