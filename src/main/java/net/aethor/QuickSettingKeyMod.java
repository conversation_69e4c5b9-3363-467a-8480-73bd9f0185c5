package net.aethor;

import net.aethor.config.ModConfig;
import net.aethor.features.FOVFeature;
import net.aethor.features.FriendlyMobSoundFeature;
import net.aethor.features.HostileMobSoundFeature;
import net.aethor.features.JukeboxSoundFeature;
import net.aethor.features.MusicSoundFeature;
import net.aethor.features.RenderDistanceFeature;
import net.aethor.features.SimulationDistanceFeature;
import net.aethor.features.ZoomFeature;
import net.aethor.handlers.KeyInputHandler;
import net.aethor.handlers.MessageHandler;
import net.fabricmc.api.ClientModInitializer;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientLifecycleEvents;

/**
 * Main mod class - chỉ đóng vai trò loader và coordinator
 * Tất cả logic được delegate cho các specialized classes
 */
public class QuickSettingKeyMod implements ClientModInitializer {

	private KeyInputHandler keyInputHandler;
	private MessageHandler messageHandler;
	private RenderDistanceFeature renderDistanceFeature;
	private SimulationDistanceFeature simulationDistanceFeature;
	private FOVFeature fovFeature;
	private ZoomFeature zoomFeature;
	private HostileMobSoundFeature hostileMobSoundFeature;
	private FriendlyMobSoundFeature friendlyMobSoundFeature;
	private MusicSoundFeature musicSoundFeature;
	private JukeboxSoundFeature jukeboxSoundFeature;

	@Override
	public void onInitializeClient() {
		// Initialize handlers
		initializeHandlers();

		// Initialize features
		initializeFeatures();

		// Register features with handler
		registerFeatures();

		// Register client tick event
		registerClientTickEvent();

		// Register shutdown event
		registerShutdownEvent();

		// Log initialization completion
		logInitialization();
	}

	/**
	 * Khởi tạo các handlers
	 */
	private void initializeHandlers() {
		keyInputHandler = KeyInputHandler.getInstance();
		messageHandler = MessageHandler.getInstance();

		if (ModConfig.DEBUG_MODE) {
			System.out.println("[" + ModConfig.MOD_NAME + "] Handlers initialized");
		}
	}

	/**
	 * Khởi tạo các features
	 */
	private void initializeFeatures() {
		// Initialize render distance feature
		if (ModConfig.ENABLE_RENDER_DISTANCE) {
			renderDistanceFeature = new RenderDistanceFeature();
			messageHandler.showInitMessage("Render Distance", true);
		}

		// Initialize simulation distance feature
		if (ModConfig.ENABLE_SIMULATION_DISTANCE) {
			simulationDistanceFeature = new SimulationDistanceFeature();
			messageHandler.showInitMessage("Simulation Distance", true);
		}

		// Initialize FOV feature
		if (ModConfig.ENABLE_FOV) {
			fovFeature = new FOVFeature();
			messageHandler.showInitMessage("FOV", true);
		}

		// Initialize Zoom feature
		if (ModConfig.ENABLE_ZOOM) {
			zoomFeature = new ZoomFeature();
			messageHandler.showInitMessage("Zoom", true);
		}

		// Initialize Hostile Mob Sound feature
		if (ModConfig.ENABLE_HOSTILE_MOB_SOUND) {
			hostileMobSoundFeature = new HostileMobSoundFeature();
			messageHandler.showInitMessage("Hostile Mob Sound", true);
		}

		// Initialize Friendly Mob Sound feature
		if (ModConfig.ENABLE_FRIENDLY_MOB_SOUND) {
			friendlyMobSoundFeature = new FriendlyMobSoundFeature();
			messageHandler.showInitMessage("Friendly Mob Sound", true);
		}

		// Initialize Music Sound feature
		if (ModConfig.ENABLE_MUSIC_SOUND) {
			musicSoundFeature = new MusicSoundFeature();
			messageHandler.showInitMessage("Music Sound", true);
		}

		// Initialize Jukebox Sound feature
		if (ModConfig.ENABLE_JUKEBOX_SOUND) {
			jukeboxSoundFeature = new JukeboxSoundFeature();
			messageHandler.showInitMessage("Jukebox Sound", true);
		}
	}

	/**
	 * Đăng ký features với handler
	 */
	private void registerFeatures() {
		if (renderDistanceFeature != null) {
			keyInputHandler.registerFeature(renderDistanceFeature);
		}

		if (simulationDistanceFeature != null) {
			keyInputHandler.registerFeature(simulationDistanceFeature);
		}

		if (fovFeature != null) {
			keyInputHandler.registerFeature(fovFeature);
		}

		if (zoomFeature != null) {
			keyInputHandler.registerZoomFeature(zoomFeature);
		}

		if (hostileMobSoundFeature != null) {
			keyInputHandler.registerHostileMobSoundFeature(hostileMobSoundFeature);
		}

		if (friendlyMobSoundFeature != null) {
			keyInputHandler.registerFriendlyMobSoundFeature(friendlyMobSoundFeature);
		}

		if (musicSoundFeature != null) {
			keyInputHandler.registerMusicSoundFeature(musicSoundFeature);
		}

		if (jukeboxSoundFeature != null) {
			keyInputHandler.registerJukeboxSoundFeature(jukeboxSoundFeature);
		}

		// Validate all registered features
		if (!keyInputHandler.validateFeatures()) {
			System.err.println("[" + ModConfig.MOD_NAME + "] Feature validation failed!");
		}
	}

	/**
	 * Đăng ký client tick event
	 */
	private void registerClientTickEvent() {
		ClientTickEvents.END_CLIENT_TICK.register(client -> {
			try {
				keyInputHandler.handleAllInputs(client);

				// Khởi tạo hostile mob sound feature với client khi có sẵn (chỉ một lần)
				if (hostileMobSoundFeature != null && client.options != null) {
					hostileMobSoundFeature.initialize(client);
					// Không set hostileMobSoundFeature = null vì vẫn cần sử dụng
				}

				// Khởi tạo friendly mob sound feature với client khi có sẵn (chỉ một lần)
				if (friendlyMobSoundFeature != null && client.options != null) {
					friendlyMobSoundFeature.initialize(client);
					// Không set friendlyMobSoundFeature = null vì vẫn cần sử dụng
				}

				// Khởi tạo music sound feature với client khi có sẵn (chỉ một lần)
				if (musicSoundFeature != null && client.options != null) {
					musicSoundFeature.initialize(client);
					// Không set musicSoundFeature = null vì vẫn cần sử dụng
				}

			// Khởi tạo jukebox sound feature với client khi có sẵn (chỉ một lần)
			if (jukeboxSoundFeature != null && client.options != null) {
				jukeboxSoundFeature.initialize(client);
				// Không set jukeboxSoundFeature = null vì vẫn cần sử dụng
			}
			} catch (Exception e) {
				System.err.println("[" + ModConfig.MOD_NAME + "] Error in client tick event: " + e.getMessage());
				if (ModConfig.DEBUG_MODE) {
					System.err.println("[" + ModConfig.MOD_NAME + "] Stack trace: " + e.getClass().getSimpleName() + " at " + e.getStackTrace()[0]);
				}
			}
		});
	}

	/**
	 * Đăng ký shutdown event để cleanup features
	 * Gọi KeyInputHandler.clearAllFeatures() khi client shutdown
	 */
	private void registerShutdownEvent() {
		ClientLifecycleEvents.CLIENT_STOPPING.register(client -> {
			try {
				if (keyInputHandler != null) {
					// Cleanup tất cả features khi shutdown
					// Method này reset zoom, mob sound và clear distance features
					keyInputHandler.clearAllFeatures();

					if (ModConfig.DEBUG_MODE) {
						System.out.println("[" + ModConfig.MOD_NAME + "] All features cleared on shutdown");
					}
				}
			} catch (Exception e) {
				System.err.println("[" + ModConfig.MOD_NAME + "] Error during shutdown cleanup: " + e.getMessage());
				if (ModConfig.DEBUG_MODE) {
					System.err.println("[" + ModConfig.MOD_NAME + "] Shutdown error stack trace: " + e.getClass().getSimpleName() + " at " + e.getStackTrace()[0]);
				}
			}
		});

		if (ModConfig.DEBUG_MODE) {
			System.out.println("[" + ModConfig.MOD_NAME + "] Shutdown event registered");
		}
	}

	/**
	 * Log thông tin initialization
	 */
	private void logInitialization() {
		int featureCount = keyInputHandler.getEnabledFeatureCount();

		System.out.println("[" + ModConfig.MOD_NAME + "] Successfully initialized with " +
				featureCount + " features");

		if (ModConfig.DEBUG_MODE) {
			System.out.println("[" + ModConfig.MOD_NAME + "] Debug mode is enabled");
			System.out.println("[" + ModConfig.MOD_NAME + "] Initialization completed with " + featureCount + " features");
		}
	}


}