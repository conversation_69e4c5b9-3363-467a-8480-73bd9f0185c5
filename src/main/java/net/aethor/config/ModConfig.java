package net.aethor.config;

/**
 * <PERSON><PERSON><PERSON> hình chung cho QuickSettingKeyMod
 * Chứa các constants và settings được sử dụng trong toàn bộ mod
 */
public class ModConfig {

    // Mod Information
    public static final String MOD_ID = "quicksettingkeymod";
    public static final String MOD_NAME = "QuickSettingKeyMod";

    // Render Distance Settings
    public static final int MIN_RENDER_DISTANCE = 2;
    public static final int MAX_RENDER_DISTANCE = 32;
    public static final int DEFAULT_RENDER_STEP = 1;

    // Simulation Distance Settings
    public static final int MIN_SIMULATION_DISTANCE = 5;
    public static final int MAX_SIMULATION_DISTANCE = 32;
    public static final int DEFAULT_SIMULATION_STEP = 1;

    // FOV Settings
    public static final int MIN_FOV = 30;
    public static final int MAX_FOV = 110;
    public static final int DEFAULT_FOV_STEP = 5;

    // Zoom Settings
    public static final int ZOOM_FOV = 30; // FOV khi zoom
    public static final int DEFAULT_FOV = 70; // FOV mặc định

    // Debouncing Settings
    public static final int DEBOUNCE_DELAY_MS = 2000; // milliseconds - delay before applying changes

    // Feature Enable/Disable Flags
    public static final boolean ENABLE_RENDER_DISTANCE = true;
    public static final boolean ENABLE_SIMULATION_DISTANCE = true;
    public static final boolean ENABLE_FOV = true;
    public static final boolean ENABLE_BRIGHTNESS = true;
    public static final boolean ENABLE_ZOOM = true;
    public static final boolean ENABLE_HOSTILE_MOB_SOUND = true;
    public static final boolean ENABLE_FRIENDLY_MOB_SOUND = true;
    public static final boolean ENABLE_MUSIC_SOUND = true;
    public static final boolean ENABLE_JUKEBOX_SOUND = true;

    // Mob Sound Settings
    /**
     * Giá trị âm lượng mặc định để restore khi người chơi ban đầu đã tắt âm thanh mobs (cả hostile và neutral = 0)
     * Khi toggle từ OFF sang ON, sẽ sử dụng giá trị này thay vì 0
     * Giá trị từ 0.0 (tắt) đến 1.0 (tối đa), 0.5 = 50% âm lượng
     */
    public static final double MOB_SOUND_DEFAULT_RESTORE_VOLUME = 0.5;

    // Music Sound Settings
    /**
     * Giá trị âm lượng mặc định để restore khi người chơi ban đầu đã tắt âm thanh nhạc (music = 0)
     * Khi toggle từ OFF sang ON, sẽ sử dụng giá trị này thay vì 0
     * Giá trị từ 0.0 (tắt) đến 1.0 (tối đa), 0.5 = 50% âm lượng
     */
    public static final double MUSIC_SOUND_DEFAULT_RESTORE_VOLUME = 0.5;

    // Jukebox Sound Settings
    /**
     * Giá trị âm lượng mặc định để restore khi người chơi ban đầu đã tắt âm thanh jukebox (record = 0)
     * Khi toggle từ OFF sang ON, sẽ sử dụng giá trị này thay vì 0
     * Giá trị từ 0.0 (tắt) đến 1.0 (tối đa), 0.5 = 50% âm lượng
     */
    public static final double JUKEBOX_SOUND_DEFAULT_RESTORE_VOLUME = 0.5;

    // Brightness Settings
    /**
     * Giá trị brightness tối thiểu (0% = tối nhất)
     * Minecraft mặc định: 0.0 = Moody
     */
    public static final double BRIGHTNESS_MIN = 0.0;

    /**
     * Giá trị brightness tối đa (1600% = sáng nhất)
     * Minecraft mặc định: 1.0 = Bright, nhưng chúng ta cho phép lên đến 16.0 (1600%)
     */
    public static final double BRIGHTNESS_MAX = 16.0;

    /**
     * Bước tăng/giảm brightness mỗi lần nhấn phím
     * 0.5 = tăng/giảm 50% mỗi lần (từ 0% đến 1600% sẽ có 32 bước)
     */
    public static final double BRIGHTNESS_STEP = 0.5;

    // Debug Settings
    public static final boolean DEBUG_MODE = false;

    private ModConfig() {
        // Utility class - không cho phép khởi tạo
    }

    /**
     * Clamp render distance vào giới hạn cho phép
     */
    public static int clampRenderDistance(int distance) {
        return Math.max(MIN_RENDER_DISTANCE, Math.min(MAX_RENDER_DISTANCE, distance));
    }

    /**
     * Clamp simulation distance vào giới hạn cho phép
     */
    public static int clampSimulationDistance(int distance) {
        return Math.max(MIN_SIMULATION_DISTANCE, Math.min(MAX_SIMULATION_DISTANCE, distance));
    }

    /**
     * Clamp FOV vào giới hạn cho phép
     */
    public static int clampFOV(int fov) {
        return Math.max(MIN_FOV, Math.min(MAX_FOV, fov));
    }
}