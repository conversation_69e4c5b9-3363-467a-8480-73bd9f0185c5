package net.aethor.utils;

import net.aethor.config.ModConfig;

/**
 * <PERSON><PERSON><PERSON><PERSON> lý tất cả translation keys đư<PERSON>c sử dụng trong mod
 * Centralized key management để tránh typo và dễ bảo trì
 */
public class TranslationKeys {

    private static final String BASE_KEY = "key." + ModConfig.MOD_ID + ".";
    private static final String CATEGORY_KEY = "category." + ModConfig.MOD_ID + ".";
    private static final String MESSAGE_KEY = "message." + ModConfig.MOD_ID + ".";

    // Base Feature Names
    public static final String RENDER_DISTANCE = "render_distance";
    public static final String SIMULATION_DISTANCE = "simulation_distance";
    public static final String FOV = "fov";
    public static final String HOSTILE_MOB_SOUND = "hostile_mob_sound";
    public static final String FRIENDLY_MOB_SOUND = "friendly_mob_sound";
    public static final String MUSIC_SOUND = "music_sound";
    public static final String JUKEBOX_SOUND = "jukebox_sound";

    // Action Types
    public static final String INCREASE = "increase";
    public static final String DECREASE = "decrease";

    // Key Binding Keys - Render Distance
    public static final String INCREASE_RENDER_DISTANCE = BASE_KEY + INCREASE + "_" + RENDER_DISTANCE;
    public static final String DECREASE_RENDER_DISTANCE = BASE_KEY + DECREASE + "_" + RENDER_DISTANCE;

    // Key Binding Keys - Simulation Distance
    public static final String INCREASE_SIMULATION_DISTANCE = BASE_KEY + INCREASE + "_" + SIMULATION_DISTANCE;
    public static final String DECREASE_SIMULATION_DISTANCE = BASE_KEY + DECREASE + "_" + SIMULATION_DISTANCE;

    // Key Binding Keys - FOV
    public static final String INCREASE_FOV = BASE_KEY + INCREASE + "_" + FOV;
    public static final String DECREASE_FOV = BASE_KEY + DECREASE + "_" + FOV;

    // Key Binding Keys - Zoom (hold-to-zoom)
    public static final String TOGGLE_ZOOM = BASE_KEY + "zoom";

    // Key Binding Keys - Hostile Mob Sound
    public static final String TOGGLE_HOSTILE_MOB_SOUND = BASE_KEY + "toggle_" + HOSTILE_MOB_SOUND;

    // Key Binding Keys - Friendly Mob Sound
    public static final String TOGGLE_FRIENDLY_MOB_SOUND = BASE_KEY + "toggle_" + FRIENDLY_MOB_SOUND;

    // Key Binding Keys - Music Sound
    public static final String TOGGLE_MUSIC_SOUND = BASE_KEY + "toggle_" + MUSIC_SOUND;

    // Key Binding Keys - Jukebox Sound
    public static final String TOGGLE_JUKEBOX_SOUND = BASE_KEY + "toggle_" + JUKEBOX_SOUND;

    // Category Keys
    public static final String QUICK_SETTING_CATEGORY = CATEGORY_KEY + "quick_settings";

    // Message Types
    public static final String CHANGED = "changed";
    public static final String MAX = "max";
    public static final String MIN = "min";

    // Message Keys - Render Distance
    public static final String RENDER_DISTANCE_CHANGED = MESSAGE_KEY + RENDER_DISTANCE + "_" + CHANGED;
    public static final String RENDER_DISTANCE_MAX = MESSAGE_KEY + RENDER_DISTANCE + "_" + MAX;
    public static final String RENDER_DISTANCE_MIN = MESSAGE_KEY + RENDER_DISTANCE + "_" + MIN;

    // Message Keys - Simulation Distance
    public static final String SIMULATION_DISTANCE_CHANGED = MESSAGE_KEY + SIMULATION_DISTANCE + "_" + CHANGED;
    public static final String SIMULATION_DISTANCE_MAX = MESSAGE_KEY + SIMULATION_DISTANCE + "_" + MAX;
    public static final String SIMULATION_DISTANCE_MIN = MESSAGE_KEY + SIMULATION_DISTANCE + "_" + MIN;

    // Message Keys - FOV
    public static final String FOV_CHANGED = MESSAGE_KEY + FOV + "_" + CHANGED;
    public static final String FOV_MAX = MESSAGE_KEY + FOV + "_" + MAX;
    public static final String FOV_MIN = MESSAGE_KEY + FOV + "_" + MIN;



    // Message Keys - General
    public static final String DISTANCE_PREVIEW = MESSAGE_KEY + "distance_preview";

    // Feature Name Translation Keys
    public static final String RENDER_DISTANCE_NAME = MESSAGE_KEY + "render_distance_name";
    public static final String SIMULATION_DISTANCE_NAME = MESSAGE_KEY + "simulation_distance_name";
    public static final String FOV_NAME = MESSAGE_KEY + "fov_name";
    public static final String HOSTILE_MOB_SOUND_NAME = MESSAGE_KEY + "hostile_mob_sound_name";
    public static final String FRIENDLY_MOB_SOUND_NAME = MESSAGE_KEY + "friendly_mob_sound_name";
    public static final String MUSIC_SOUND_NAME = MESSAGE_KEY + "music_sound_name";
    public static final String JUKEBOX_SOUND_NAME = MESSAGE_KEY + "jukebox_sound_name";

    // Status Messages
    public static final String MESSAGE_ON = MESSAGE_KEY + "on";
    public static final String MESSAGE_OFF = MESSAGE_KEY + "off";

    // Message Type Translation Keys
    public static final String MESSAGE_MAX = MESSAGE_KEY + "message_max";
    public static final String MESSAGE_MIN = MESSAGE_KEY + "message_min";

    // Feature Names (for logging/debug) - sử dụng translation keys
    public static final String RENDER_DISTANCE_FEATURE = "Render Distance";
    public static final String SIMULATION_DISTANCE_FEATURE = "Simulation Distance";
    public static final String FOV_FEATURE = "FOV";
    public static final String ZOOM_FEATURE = "Zoom";
    public static final String HOSTILE_MOB_SOUND_FEATURE = "Hostile Mob Sound";
    public static final String FRIENDLY_MOB_SOUND_FEATURE = "Friendly Mob Sound";
    public static final String MUSIC_SOUND_FEATURE = "Music Sound";
    public static final String JUKEBOX_SOUND_FEATURE = "Jukebox Sound";

    private TranslationKeys() {
        // Utility class - không cho phép khởi tạo
    }

}