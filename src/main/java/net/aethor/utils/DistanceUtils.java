package net.aethor.utils;

import net.aethor.config.ModConfig;
import net.minecraft.client.MinecraftClient;

/**
 * Utility class cung cấp các helper methods cho distance operations
 * Chứa logic chung có thể được sử dụng bởi nhiều features
 */
public class DistanceUtils {

    private DistanceUtils() {
        // Utility class - không cho phép khởi tạo
    }

    /**
     * Tính toán performance impact dựa trên render distance
     * Tr<PERSON> về mức độ impact từ 1-5 (1 = low, 5 = very high)
     */
    public static int calculateRenderPerformanceImpact(int renderDistance) {
        if (renderDistance <= 6) return 1;      // Low impact
        if (renderDistance <= 12) return 2;     // Medium-low impact
        if (renderDistance <= 18) return 3;     // Medium impact
        if (renderDistance <= 24) return 4;     // High impact
        return 5;                               // Very high impact
    }

    /**
     * Tính toán performance impact dựa trên simulation distance
     */
    public static int calculateSimulationPerformanceImpact(int simulationDistance) {
        if (simulationDistance <= 8) return 1;   // Low impact
        if (simulationDistance <= 16) return 2;  // Medium impact
        if (simulationDistance <= 24) return 3;  // High impact
        return 4;                                // Very high impact
    }

    /**
     * Đề xuất render distance tối ưu dựa trên performance
     */
    public static int suggestOptimalRenderDistance() {
        // Logic có thể phức tạp hơn, tạm thời return giá trị cố định
        return 12; // Balanced performance và quality
    }

    /**
     * Đề xuất simulation distance tối ưu
     */
    public static int suggestOptimalSimulationDistance() {
        return 8; // Reasonable simulation range
    }

    /**
     * Tính số chunks được render
     */
    public static int calculateRenderedChunks(int renderDistance) {
        // Formula: (2 * distance + 1)^2
        int diameter = 2 * renderDistance + 1;
        return diameter * diameter;
    }

    /**
     * Tính số chunks được simulate
     */
    public static int calculateSimulatedChunks(int simulationDistance) {
        int diameter = 2 * simulationDistance + 1;
        return diameter * diameter;
    }

    /**
     * Estimate memory usage cho render distance (MB)
     */
    public static long estimateRenderMemoryUsage(int renderDistance) {
        int chunks = calculateRenderedChunks(renderDistance);
        // Rough estimate: ~1MB per chunk (very approximate)
        return chunks;
    }

    /**
     * Validate distance value và clamp nếu cần
     */
    public static int validateAndClampRenderDistance(int distance) {
        return ModConfig.clampRenderDistance(distance);
    }

    /**
     * Validate simulation distance
     */
    public static int validateAndClampSimulationDistance(int distance) {
        return ModConfig.clampSimulationDistance(distance);
    }

    /**
     * Kiểm tra xem distance có trong safe range không
     */
    public static boolean isRenderDistanceSafe(int distance) {
        return distance >= ModConfig.MIN_RENDER_DISTANCE &&
                distance <= 16; // Conservative safe range
    }

    /**
     * Kiểm tra simulation distance có safe không
     */
    public static boolean isSimulationDistanceSafe(int distance) {
        return distance >= ModConfig.MIN_SIMULATION_DISTANCE &&
                distance <= 12; // Conservative safe range
    }

    /**
     * Format distance value thành string có thể hiểu được
     */
    public static String formatDistanceInfo(int distance, String type) {
        StringBuilder info = new StringBuilder();
        info.append(distance).append(" chunks");

        if ("render".equalsIgnoreCase(type)) {
            int chunks = calculateRenderedChunks(distance);
            int impact = calculateRenderPerformanceImpact(distance);
            info.append(" (").append(chunks).append(" total chunks, ");
            info.append("impact: ").append(getImpactDescription(impact)).append(")");
        } else if ("simulation".equalsIgnoreCase(type)) {
            int chunks = calculateSimulatedChunks(distance);
            int impact = calculateSimulationPerformanceImpact(distance);
            info.append(" (").append(chunks).append(" active chunks, ");
            info.append("impact: ").append(getImpactDescription(impact)).append(")");
        }

        return info.toString();
    }

    /**
     * Convert impact level thành description
     */
    private static String getImpactDescription(int impact) {
        switch (impact) {
            case 1: return "Low";
            case 2: return "Medium-Low";
            case 3: return "Medium";
            case 4: return "High";
            case 5: return "Very High";
            default: return "Unknown";
        }
    }

    /**
     * Tính toán recommended settings dựa trên system specs
     * (Tạm thời return default values, có thể extend sau)
     */
    public static DistanceRecommendation getRecommendedSettings() {
        // Có thể detect system specs và recommend accordingly
        return new DistanceRecommendation(12, 8);
    }

    /**
     * Helper class cho recommendations
     */
    public static class DistanceRecommendation {
        private final int recommendedRenderDistance;
        private final int recommendedSimulationDistance;

        public DistanceRecommendation(int renderDistance, int simulationDistance) {
            this.recommendedRenderDistance = validateAndClampRenderDistance(renderDistance);
            this.recommendedSimulationDistance = validateAndClampSimulationDistance(simulationDistance);
        }

        public int getRenderDistance() {
            return recommendedRenderDistance;
        }

        public int getSimulationDistance() {
            return recommendedSimulationDistance;
        }

        @Override
        public String toString() {
            return String.format("Render: %d, Simulation: %d",
                    recommendedRenderDistance, recommendedSimulationDistance);
        }
    }

    /**
     * Apply recommended settings
     */
    public static void applyRecommendedSettings(MinecraftClient client) {
        DistanceRecommendation recommendation = getRecommendedSettings();

        client.options.getViewDistance().setValue(recommendation.getRenderDistance());
        client.options.getSimulationDistance().setValue(recommendation.getSimulationDistance());

        if (ModConfig.DEBUG_MODE) {
            System.out.println("[" + ModConfig.MOD_NAME + "] Applied recommended settings: " + recommendation);
        }
    }
}