package net.aethor.features;

import net.aethor.config.ModConfig;
import net.aethor.utils.TranslationKeys;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import net.minecraft.sound.SoundCategory;
import net.minecraft.text.Text;

/**
 * Feature để tắt bật âm thanh neutral/friendly mobs bằng phím tắt
 * Điều khiển SoundCategory.NEUTRAL (cow, pig, villager, etc.)
 * Có 2 trạng thái: ON/OFF
 */
public class NeutralMobSoundFeature {

    private final KeyBinding toggleKey;
    private boolean isNeutralSoundOn = true;
    
    // Lưu âm lượng gốc ban đầu của Minecraft (lấy khi khởi tạo)
    private double minecraftOriginalNeutralVolume = 1.0;
    
    // Lưu âm lượng hiện tại trước khi tắt (để restore khi bật lại)
    private double currentNeutralVolume = 1.0;
    
    // Lưu âm lượng cuối cùng để so sánh thay đổi
    private double lastKnownNeutralVolume = 1.0;
    
    // Flag để đảm bảo initialize chỉ được gọi một lần
    private boolean isInitialized = false;

    public NeutralMobSoundFeature() {
        // Đăng ký key binding cho toggle neutral mob sound
        this.toggleKey = KeyBindingHelper.registerKeyBinding(new KeyBinding(
                TranslationKeys.TOGGLE_NEUTRAL_MOB_SOUND,
                InputUtil.Type.KEYSYM,
                InputUtil.UNKNOWN_KEY.getCode(), // Không có phím mặc định
                TranslationKeys.QUICK_SETTING_CATEGORY
        ));
    }

    /**
     * Khởi tạo và lưu giá trị gốc ban đầu của Minecraft
     * Gọi method này sau khi client đã sẵn sàng (chỉ một lần)
     */
    public void initialize(MinecraftClient client) {
        if (!isInitialized && client != null && client.options != null) {
            // Lưu giá trị gốc ban đầu của Minecraft
            minecraftOriginalNeutralVolume = client.options.getSoundVolumeOption(SoundCategory.NEUTRAL).getValue();
            
            // Kiểm tra trạng thái ban đầu: nếu = 0 thì đang tắt
            boolean isOriginallyOff = (minecraftOriginalNeutralVolume == 0.0);
            
            if (isOriginallyOff) {
                // Nếu ban đầu đã tắt, set trạng thái là OFF và giá trị hiện tại là default restore volume để restore
                isNeutralSoundOn = false;
                currentNeutralVolume = ModConfig.MOB_SOUND_DEFAULT_RESTORE_VOLUME;
            } else {
                // Nếu ban đầu đang bật, set trạng thái là ON và giá trị hiện tại bằng giá trị gốc
                isNeutralSoundOn = true;
                currentNeutralVolume = minecraftOriginalNeutralVolume;
            }
            
            // Khởi tạo giá trị cuối cùng để theo dõi thay đổi
            lastKnownNeutralVolume = minecraftOriginalNeutralVolume;
            
            isInitialized = true;

            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Initialized Neutral Mob Sound - Original volume: " + 
                    minecraftOriginalNeutralVolume + ", Initial state: " + (isNeutralSoundOn ? "ON" : "OFF") + 
                    ", Default restore volume: " + ModConfig.MOB_SOUND_DEFAULT_RESTORE_VOLUME);
            }
        }
    }

    /**
     * Xử lý input từ bàn phím cho neutral mob sound feature
     */
    public void handleInput(MinecraftClient client) {
        if (client.player == null) return;

        // Theo dõi thay đổi settings âm thanh từ người dùng
        checkForSoundSettingsChanges(client);

        // Xử lý phím toggle
        while (toggleKey.wasPressed()) {
            toggleNeutralSound(client);
        }
    }

    /**
     * Theo dõi thay đổi settings âm thanh từ người dùng
     */
    private void checkForSoundSettingsChanges(MinecraftClient client) {
        if (!isInitialized || client == null || client.options == null) return;

        double currentGameNeutralVolume = client.options.getSoundVolumeOption(SoundCategory.NEUTRAL).getValue();

        // Kiểm tra xem có thay đổi không
        boolean hasChanged = (currentGameNeutralVolume != lastKnownNeutralVolume);

        if (hasChanged) {
            // Cập nhật giá trị cuối cùng
            lastKnownNeutralVolume = currentGameNeutralVolume;

            // Phân tích trạng thái mới dựa trên giá trị âm thanh
            boolean newSoundState = !(currentGameNeutralVolume == 0.0);
            
            if (newSoundState != isNeutralSoundOn) {
                // Trạng thái đã thay đổi, cập nhật
                isNeutralSoundOn = newSoundState;
                
                if (isNeutralSoundOn) {
                    // Người dùng đã bật âm thanh, lưu giá trị hiện tại
                    currentNeutralVolume = currentGameNeutralVolume;
                } else {
                    // Người dùng đã tắt âm thanh, giữ nguyên giá trị restore
                    // (không thay đổi currentNeutralVolume)
                }

                if (ModConfig.DEBUG_MODE) {
                    System.out.println("[" + ModConfig.MOD_NAME + "] User changed neutral mob settings - New state: " + 
                        (isNeutralSoundOn ? "ON" : "OFF") + ", Volume: " + currentGameNeutralVolume);
                }
            } else if (isNeutralSoundOn) {
                // Trạng thái không đổi nhưng đang bật, cập nhật giá trị hiện tại
                currentNeutralVolume = currentGameNeutralVolume;

                if (ModConfig.DEBUG_MODE) {
                    System.out.println("[" + ModConfig.MOD_NAME + "] User adjusted neutral mob volume: " + currentNeutralVolume);
                }
            }
        }
    }

    /**
     * Toggle giữa ON/OFF neutral mob sound
     */
    private void toggleNeutralSound(MinecraftClient client) {
        // Chuyển đổi trạng thái
        isNeutralSoundOn = !isNeutralSoundOn;
        
        if (isNeutralSoundOn) {
            // Bật âm thanh - restore âm lượng hiện tại
            setNeutralVolume(client, currentNeutralVolume);
            showStateMessage(client, true);
        } else {
            // Lưu âm lượng hiện tại trước khi tắt (chỉ khi không phải trường hợp đặc biệt)
            if (!(minecraftOriginalNeutralVolume == 0.0)) {
                currentNeutralVolume = client.options.getSoundVolumeOption(SoundCategory.NEUTRAL).getValue();
            }
            // Tắt âm thanh - set về 0
            setNeutralVolume(client, 0.0);
            showStateMessage(client, false);
        }

        if (ModConfig.DEBUG_MODE) {
            System.out.println("[" + ModConfig.MOD_NAME + "] Neutral mob sound state changed to: " + (isNeutralSoundOn ? "ON" : "OFF") +
                " (Current volume: " + currentNeutralVolume + ")");
        }
    }

    /**
     * Đặt âm lượng cho neutral mobs
     */
    private void setNeutralVolume(MinecraftClient client, double volume) {
        client.options.getSoundVolumeOption(SoundCategory.NEUTRAL).setValue(volume);
    }

    /**
     * Hiển thị thông báo trạng thái hiện tại
     */
    private void showStateMessage(MinecraftClient client, boolean isOn) {
        if (client.player != null) {
            Text featureName = Text.translatable(TranslationKeys.NEUTRAL_MOB_SOUND_NAME);
            Text statusText = Text.translatable(isOn ? TranslationKeys.MESSAGE_ON : TranslationKeys.MESSAGE_OFF);
            String color = isOn ? "§a" : "§c"; // Green for ON, Red for OFF
            
            client.player.sendMessage(
                Text.literal(featureName.getString() + "§7: " + color + statusText.getString()),
                true
            );
        }
    }

    /**
     * Reset neutral mob sound về giá trị gốc ban đầu của Minecraft (dùng khi thoát game)
     */
    public void resetNeutralSound(MinecraftClient client) {
        if (client != null && client.options != null) {
            // Hoàn nguyên về giá trị gốc ban đầu của Minecraft
            setNeutralVolume(client, minecraftOriginalNeutralVolume);
            isNeutralSoundOn = true;
            
            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Neutral mob sound reset to Minecraft original volume: " + 
                    minecraftOriginalNeutralVolume);
            }
        }
    }

    // Getters

    public String getFeatureName() {
        return TranslationKeys.NEUTRAL_MOB_SOUND_FEATURE;
    }

    public KeyBinding getToggleKey() {
        return toggleKey;
    }

    public boolean isEnabled() {
        return ModConfig.ENABLE_NEUTRAL_MOB_SOUND;
    }

    public boolean isNeutralSoundOn() {
        return isNeutralSoundOn;
    }

    /**
     * Lấy giá trị gốc ban đầu của Minecraft
     */
    public double getMinecraftOriginalNeutralVolume() {
        return minecraftOriginalNeutralVolume;
    }
}
