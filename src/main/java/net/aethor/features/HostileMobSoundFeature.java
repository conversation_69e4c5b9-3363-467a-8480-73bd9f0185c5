package net.aethor.features;

import net.aethor.config.ModConfig;
import net.aethor.utils.TranslationKeys;
import net.minecraft.sound.SoundCategory;

/**
 * Feature để tắt bật âm thanh hostile mobs bằng phím tắt
 * Điều khiển SoundCategory.HOSTILE (zombie, skeleton, creeper, etc.)
 * Có 2 trạng thái: ON/OFF
 */
public class HostileMobSoundFeature extends AbstractSoundFeature {

    public HostileMobSoundFeature() {
        super();
    }

    /**
     * Khởi tạo và lưu giá trị gốc ban đầu của Minecraft
     * Gọi method này sau khi client đã sẵn sàng (chỉ một lần)
     */
    public void initialize(MinecraftClient client) {
        if (!isInitialized && client != null && client.options != null) {
            // Lưu giá trị gốc ban đầu của Minecraft
            minecraftOriginalHostileVolume = client.options.getSoundVolumeOption(SoundCategory.HOSTILE).getValue();
            
            // Kiểm tra trạng thái ban đầu: nếu = 0 thì đang tắt
            boolean isOriginallyOff = (minecraftOriginalHostileVolume == 0.0);
            
            if (isOriginallyOff) {
                // Nếu ban đầu đã tắt, set trạng thái là OFF và giá trị hiện tại là default restore volume để restore
                isHostileSoundOn = false;
                currentHostileVolume = ModConfig.MOB_SOUND_DEFAULT_RESTORE_VOLUME;
            } else {
                // Nếu ban đầu đang bật, set trạng thái là ON và giá trị hiện tại bằng giá trị gốc
                isHostileSoundOn = true;
                currentHostileVolume = minecraftOriginalHostileVolume;
            }
            
            // Khởi tạo giá trị cuối cùng để theo dõi thay đổi
            lastKnownHostileVolume = minecraftOriginalHostileVolume;
            
            isInitialized = true;

            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Initialized Hostile Mob Sound - Original volume: " + 
                    minecraftOriginalHostileVolume + ", Initial state: " + (isHostileSoundOn ? "ON" : "OFF") + 
                    ", Default restore volume: " + ModConfig.MOB_SOUND_DEFAULT_RESTORE_VOLUME);
            }
        }
    }

    /**
     * Xử lý input từ bàn phím cho hostile mob sound feature
     */
    public void handleInput(MinecraftClient client) {
        if (client.player == null) return;

        // Theo dõi thay đổi settings âm thanh từ người dùng
        checkForSoundSettingsChanges(client);

        // Xử lý phím toggle
        while (toggleKey.wasPressed()) {
            toggleHostileSound(client);
        }
    }

    /**
     * Theo dõi thay đổi settings âm thanh từ người dùng
     */
    private void checkForSoundSettingsChanges(MinecraftClient client) {
        if (!isInitialized || client == null || client.options == null) return;

        double currentGameHostileVolume = client.options.getSoundVolumeOption(SoundCategory.HOSTILE).getValue();

        // Kiểm tra xem có thay đổi không
        boolean hasChanged = (currentGameHostileVolume != lastKnownHostileVolume);

        if (hasChanged) {
            // Cập nhật giá trị cuối cùng
            lastKnownHostileVolume = currentGameHostileVolume;

            // Phân tích trạng thái mới dựa trên giá trị âm thanh
            boolean newSoundState = !(currentGameHostileVolume == 0.0);
            
            if (newSoundState != isHostileSoundOn) {
                // Trạng thái đã thay đổi, cập nhật
                isHostileSoundOn = newSoundState;
                
                if (isHostileSoundOn) {
                    // Người dùng đã bật âm thanh, lưu giá trị hiện tại
                    currentHostileVolume = currentGameHostileVolume;
                } else {
                    // Người dùng đã tắt âm thanh, giữ nguyên giá trị restore
                    // (không thay đổi currentHostileVolume)
                }

                if (ModConfig.DEBUG_MODE) {
                    System.out.println("[" + ModConfig.MOD_NAME + "] User changed hostile mob settings - New state: " + 
                        (isHostileSoundOn ? "ON" : "OFF") + ", Volume: " + currentGameHostileVolume);
                }
            } else if (isHostileSoundOn) {
                // Trạng thái không đổi nhưng đang bật, cập nhật giá trị hiện tại
                currentHostileVolume = currentGameHostileVolume;

                if (ModConfig.DEBUG_MODE) {
                    System.out.println("[" + ModConfig.MOD_NAME + "] User adjusted hostile mob volume: " + currentHostileVolume);
                }
            }
        }
    }

    /**
     * Toggle giữa ON/OFF hostile mob sound
     */
    private void toggleHostileSound(MinecraftClient client) {
        // Chuyển đổi trạng thái
        isHostileSoundOn = !isHostileSoundOn;
        
        if (isHostileSoundOn) {
            // Bật âm thanh - restore âm lượng hiện tại
            setHostileVolume(client, currentHostileVolume);
            showStateMessage(client, true);
        } else {
            // Lưu âm lượng hiện tại trước khi tắt (chỉ khi không phải trường hợp đặc biệt)
            if (!(minecraftOriginalHostileVolume == 0.0)) {
                currentHostileVolume = client.options.getSoundVolumeOption(SoundCategory.HOSTILE).getValue();
            }
            // Tắt âm thanh - set về 0
            setHostileVolume(client, 0.0);
            showStateMessage(client, false);
        }

        if (ModConfig.DEBUG_MODE) {
            System.out.println("[" + ModConfig.MOD_NAME + "] Hostile mob sound state changed to: " + (isHostileSoundOn ? "ON" : "OFF") +
                " (Current volume: " + currentHostileVolume + ")");
        }
    }

    /**
     * Đặt âm lượng cho hostile mobs
     */
    private void setHostileVolume(MinecraftClient client, double volume) {
        client.options.getSoundVolumeOption(SoundCategory.HOSTILE).setValue(volume);
    }

    /**
     * Hiển thị thông báo trạng thái hiện tại
     */
    private void showStateMessage(MinecraftClient client, boolean isOn) {
        if (client.player != null) {
            Text featureName = Text.translatable(TranslationKeys.HOSTILE_MOB_SOUND_NAME);
            Text statusText = Text.translatable(isOn ? TranslationKeys.MESSAGE_ON : TranslationKeys.MESSAGE_OFF);
            String color = isOn ? "§a" : "§c"; // Green for ON, Red for OFF
            
            client.player.sendMessage(
                Text.literal(featureName.getString() + "§7: " + color + statusText.getString()),
                true
            );
        }
    }

    /**
     * Reset hostile mob sound về giá trị gốc ban đầu của Minecraft (dùng khi thoát game)
     */
    public void resetHostileSound(MinecraftClient client) {
        if (client != null && client.options != null) {
            // Hoàn nguyên về giá trị gốc ban đầu của Minecraft
            setHostileVolume(client, minecraftOriginalHostileVolume);
            isHostileSoundOn = true;
            
            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Hostile mob sound reset to Minecraft original volume: " + 
                    minecraftOriginalHostileVolume);
            }
        }
    }

    // Getters

    public String getFeatureName() {
        return TranslationKeys.HOSTILE_MOB_SOUND_FEATURE;
    }

    public KeyBinding getToggleKey() {
        return toggleKey;
    }

    public boolean isEnabled() {
        return ModConfig.ENABLE_HOSTILE_MOB_SOUND;
    }

    public boolean isHostileSoundOn() {
        return isHostileSoundOn;
    }

    /**
     * Lấy giá trị gốc ban đầu của Minecraft
     */
    public double getMinecraftOriginalHostileVolume() {
        return minecraftOriginalHostileVolume;
    }
}
