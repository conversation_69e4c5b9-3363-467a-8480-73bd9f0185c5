package net.aethor.features;

import net.aethor.config.ModConfig;
import net.aethor.utils.TranslationKeys;
import net.minecraft.sound.SoundCategory;

/**
 * Feature để tắt bật âm thanh friendly mobs bằng phím tắt
 * Điều khiển SoundCategory.NEUTRAL (cow, pig, villager, etc.)
 * Có 2 trạng thái: ON/OFF
 */
public class FriendlyMobSoundFeature extends AbstractSoundFeature {

    public FriendlyMobSoundFeature() {
        super();
    }

    /**
     * Khởi tạo và lưu giá trị gốc ban đầu của Minecraft
     * Gọi method này sau khi client đã sẵn sàng (chỉ một lần)
     */
    public void initialize(MinecraftClient client) {
        if (!isInitialized && client != null && client.options != null) {
            // Lưu giá trị gốc ban đầu của Minecraft
            minecraftOriginalFriendlyVolume = client.options.getSoundVolumeOption(SoundCategory.NEUTRAL).getValue();

            // Kiểm tra trạng thái ban đầu: nếu = 0 thì đang tắt
            boolean isOriginallyOff = (minecraftOriginalFriendlyVolume == 0.0);

            if (isOriginallyOff) {
                // Nếu ban đầu đã tắt, set trạng thái là OFF và giá trị hiện tại là default restore volume để restore
                isFriendlySoundOn = false;
                currentFriendlyVolume = ModConfig.MOB_SOUND_DEFAULT_RESTORE_VOLUME;
            } else {
                // Nếu ban đầu đang bật, set trạng thái là ON và giá trị hiện tại bằng giá trị gốc
                isFriendlySoundOn = true;
                currentFriendlyVolume = minecraftOriginalFriendlyVolume;
            }

            // Khởi tạo giá trị cuối cùng để theo dõi thay đổi
            lastKnownFriendlyVolume = minecraftOriginalFriendlyVolume;
            
            isInitialized = true;

            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Initialized Friendly Mob Sound - Original volume: " +
                    minecraftOriginalFriendlyVolume + ", Initial state: " + (isFriendlySoundOn ? "ON" : "OFF") +
                    ", Default restore volume: " + ModConfig.MOB_SOUND_DEFAULT_RESTORE_VOLUME);
            }
        }
    }

    /**
     * Xử lý input từ bàn phím cho friendly mob sound feature
     */
    public void handleInput(MinecraftClient client) {
        if (client.player == null) return;

        // Theo dõi thay đổi settings âm thanh từ người dùng
        checkForSoundSettingsChanges(client);

        // Xử lý phím toggle
        while (toggleKey.wasPressed()) {
            toggleFriendlySound(client);
        }
    }

    /**
     * Theo dõi thay đổi settings âm thanh từ người dùng
     */
    private void checkForSoundSettingsChanges(MinecraftClient client) {
        if (!isInitialized || client == null || client.options == null) return;

        double currentGameFriendlyVolume = client.options.getSoundVolumeOption(SoundCategory.NEUTRAL).getValue();

        // Kiểm tra xem có thay đổi không
        boolean hasChanged = (currentGameFriendlyVolume != lastKnownFriendlyVolume);

        if (hasChanged) {
            // Cập nhật giá trị cuối cùng
            lastKnownFriendlyVolume = currentGameFriendlyVolume;

            // Phân tích trạng thái mới dựa trên giá trị âm thanh
            boolean newSoundState = !(currentGameFriendlyVolume == 0.0);

            if (newSoundState != isFriendlySoundOn) {
                // Trạng thái đã thay đổi, cập nhật
                isFriendlySoundOn = newSoundState;

                if (isFriendlySoundOn) {
                    // Người dùng đã bật âm thanh, lưu giá trị hiện tại
                    currentFriendlyVolume = currentGameFriendlyVolume;
                } else {
                    // Người dùng đã tắt âm thanh, giữ nguyên giá trị restore
                    // (không thay đổi currentFriendlyVolume)
                }

                if (ModConfig.DEBUG_MODE) {
                    System.out.println("[" + ModConfig.MOD_NAME + "] User changed friendly mob settings - New state: " +
                        (isFriendlySoundOn ? "ON" : "OFF") + ", Volume: " + currentGameFriendlyVolume);
                }
            } else if (isFriendlySoundOn) {
                // Trạng thái không đổi nhưng đang bật, cập nhật giá trị hiện tại
                currentFriendlyVolume = currentGameFriendlyVolume;

                if (ModConfig.DEBUG_MODE) {
                    System.out.println("[" + ModConfig.MOD_NAME + "] User adjusted friendly mob volume: " + currentFriendlyVolume);
                }
            }
        }
    }

    /**
     * Toggle giữa ON/OFF friendly mob sound
     */
    private void toggleFriendlySound(MinecraftClient client) {
        // Chuyển đổi trạng thái
        isFriendlySoundOn = !isFriendlySoundOn;

        if (isFriendlySoundOn) {
            // Bật âm thanh - restore âm lượng hiện tại
            setFriendlyVolume(client, currentFriendlyVolume);
            showStateMessage(client, true);
        } else {
            // Lưu âm lượng hiện tại trước khi tắt (chỉ khi không phải trường hợp đặc biệt)
            if (!(minecraftOriginalFriendlyVolume == 0.0)) {
                currentFriendlyVolume = client.options.getSoundVolumeOption(SoundCategory.NEUTRAL).getValue();
            }
            // Tắt âm thanh - set về 0
            setFriendlyVolume(client, 0.0);
            showStateMessage(client, false);
        }

        if (ModConfig.DEBUG_MODE) {
            System.out.println("[" + ModConfig.MOD_NAME + "] Friendly mob sound state changed to: " + (isFriendlySoundOn ? "ON" : "OFF") +
                " (Current volume: " + currentFriendlyVolume + ")");
        }
    }

    /**
     * Đặt âm lượng cho friendly mobs
     */
    private void setFriendlyVolume(MinecraftClient client, double volume) {
        client.options.getSoundVolumeOption(SoundCategory.NEUTRAL).setValue(volume);
    }

    /**
     * Hiển thị thông báo trạng thái hiện tại
     */
    private void showStateMessage(MinecraftClient client, boolean isOn) {
        if (client.player != null) {
            Text featureName = Text.translatable(TranslationKeys.FRIENDLY_MOB_SOUND_NAME);
            Text statusText = Text.translatable(isOn ? TranslationKeys.MESSAGE_ON : TranslationKeys.MESSAGE_OFF);
            String color = isOn ? "§a" : "§c"; // Green for ON, Red for OFF

            client.player.sendMessage(
                Text.literal(featureName.getString() + "§7: " + color + statusText.getString()),
                true
            );
        }
    }

    /**
     * Reset friendly mob sound về giá trị gốc ban đầu của Minecraft (dùng khi thoát game)
     */
    public void resetFriendlySound(MinecraftClient client) {
        if (client != null && client.options != null) {
            // Hoàn nguyên về giá trị gốc ban đầu của Minecraft
            setFriendlyVolume(client, minecraftOriginalFriendlyVolume);
            isFriendlySoundOn = true;

            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Friendly mob sound reset to Minecraft original volume: " +
                    minecraftOriginalFriendlyVolume);
            }
        }
    }

    // Getters

    public String getFeatureName() {
        return TranslationKeys.FRIENDLY_MOB_SOUND_FEATURE;
    }

    public KeyBinding getToggleKey() {
        return toggleKey;
    }

    public boolean isEnabled() {
        return ModConfig.ENABLE_FRIENDLY_MOB_SOUND;
    }

    public boolean isFriendlySoundOn() {
        return isFriendlySoundOn;
    }

    /**
     * Lấy giá trị gốc ban đầu của Minecraft
     */
    public double getMinecraftOriginalFriendlyVolume() {
        return minecraftOriginalFriendlyVolume;
    }
}
