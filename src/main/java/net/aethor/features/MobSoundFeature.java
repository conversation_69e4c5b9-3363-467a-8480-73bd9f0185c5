package net.aethor.features;

import net.aethor.config.ModConfig;
import net.aethor.utils.TranslationKeys;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import net.minecraft.sound.SoundCategory;
import net.minecraft.text.Text;

/**
 * Feature để tắt bật âm thanh của mobs bằng phím tắt
 * Có 2 trạng thái: ON/OFF cho cả hostile và friendly mobs
 * - ON: Cả hostile và friendly mobs đều có âm thanh
 * - OFF: Tắt âm thanh cả hostile và friendly mobs
 */
public class MobSoundFeature {

    private final KeyBinding toggleKey;
    private boolean isMobSoundOn = true;

    // Lưu âm lượng gốc ban đầu của Minecraft (lấy khi khởi tạo)
    private double minecraftOriginalHostileVolume = 1.0;
    private double minecraftOriginalNeutralVolume = 1.0;

    // Lưu âm lượng hiện tại trước khi tắt (để restore khi bật lại)
    private double currentHostileVolume = 1.0;
    private double currentNeutralVolume = 1.0;

    // Lưu âm lượng cuối cùng để so sánh thay đổi
    private double lastKnownHostileVolume = 1.0;
    private double lastKnownNeutralVolume = 1.0;

    // Flag để đảm bảo initialize chỉ được gọi một lần
    private boolean isInitialized = false;

    public MobSoundFeature() {
        // Đăng ký key binding cho toggle mob sound
        this.toggleKey = KeyBindingHelper.registerKeyBinding(new KeyBinding(
                TranslationKeys.TOGGLE_MOB_SOUND,
                InputUtil.Type.KEYSYM,
                InputUtil.UNKNOWN_KEY.getCode(), // Không có phím mặc định
                TranslationKeys.QUICK_SETTING_CATEGORY
        ));
    }

    /**
     * Khởi tạo và lưu giá trị gốc ban đầu của Minecraft
     * Gọi method này sau khi client đã sẵn sàng (chỉ một lần)
     */
    public void initialize(MinecraftClient client) {
        if (!isInitialized && client != null && client.options != null) {
            // Lưu giá trị gốc ban đầu của Minecraft
            minecraftOriginalHostileVolume = client.options.getSoundVolumeOption(SoundCategory.HOSTILE).getValue();
            minecraftOriginalNeutralVolume = client.options.getSoundVolumeOption(SoundCategory.NEUTRAL).getValue();

            // Kiểm tra trạng thái ban đầu: nếu cả 2 đều = 0 thì đang tắt
            boolean isOriginallyOff = (minecraftOriginalHostileVolume == 0.0 && minecraftOriginalNeutralVolume == 0.0);

            if (isOriginallyOff) {
                // Nếu ban đầu đã tắt, set trạng thái là OFF và giá trị hiện tại là default restore volume để restore
                isMobSoundOn = false;
                currentHostileVolume = ModConfig.MOB_SOUND_DEFAULT_RESTORE_VOLUME;
                currentNeutralVolume = ModConfig.MOB_SOUND_DEFAULT_RESTORE_VOLUME;
            } else {
                // Nếu ban đầu đang bật, set trạng thái là ON và giá trị hiện tại bằng giá trị gốc
                isMobSoundOn = true;
                currentHostileVolume = minecraftOriginalHostileVolume;
                currentNeutralVolume = minecraftOriginalNeutralVolume;
            }

            // Khởi tạo giá trị cuối cùng để theo dõi thay đổi
            lastKnownHostileVolume = minecraftOriginalHostileVolume;
            lastKnownNeutralVolume = minecraftOriginalNeutralVolume;

            isInitialized = true;

            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Initialized Mob Sound - Original volumes: Hostile=" +
                    minecraftOriginalHostileVolume + ", Neutral=" + minecraftOriginalNeutralVolume +
                    ", Initial state: " + (isMobSoundOn ? "ON" : "OFF") +
                    ", Default restore volume: " + ModConfig.MOB_SOUND_DEFAULT_RESTORE_VOLUME);
            }
        }
    }

    /**
     * Xử lý input từ bàn phím cho mob sound feature
     */
    public void handleInput(MinecraftClient client) {
        if (client.player == null) return;

        // Theo dõi thay đổi settings âm thanh từ người dùng
        checkForSoundSettingsChanges(client);

        // Xử lý phím toggle
        while (toggleKey.wasPressed()) {
            toggleMobSound(client);
        }
    }

    /**
     * Toggle giữa ON/OFF mob sound
     */
    private void toggleMobSound(MinecraftClient client) {
        // Chuyển đổi trạng thái
        isMobSoundOn = !isMobSoundOn;

        if (isMobSoundOn) {
            // Bật âm thanh - restore âm lượng hiện tại
            setHostileVolume(client, currentHostileVolume);
            setNeutralVolume(client, currentNeutralVolume);
            showStateMessage(client, true);
        } else {
            // Lưu âm lượng hiện tại trước khi tắt (chỉ khi không phải trường hợp đặc biệt)
            if (!(minecraftOriginalHostileVolume == 0.0 && minecraftOriginalNeutralVolume == 0.0)) {
                currentHostileVolume = client.options.getSoundVolumeOption(SoundCategory.HOSTILE).getValue();
                currentNeutralVolume = client.options.getSoundVolumeOption(SoundCategory.NEUTRAL).getValue();
            }
            // Tắt âm thanh - set về 0
            setHostileVolume(client, 0.0);
            setNeutralVolume(client, 0.0);
            showStateMessage(client, false);
        }

        if (ModConfig.DEBUG_MODE) {
            System.out.println("[" + ModConfig.MOD_NAME + "] Mob sound state changed to: " + (isMobSoundOn ? "ON" : "OFF") +
                " (Current volumes: Hostile=" + currentHostileVolume + ", Neutral=" + currentNeutralVolume + ")");
        }
    }

    /**
     * Theo dõi thay đổi settings âm thanh từ người dùng
     */
    private void checkForSoundSettingsChanges(MinecraftClient client) {
        if (!isInitialized || client == null || client.options == null) return;

        double currentGameHostileVolume = client.options.getSoundVolumeOption(SoundCategory.HOSTILE).getValue();
        double currentGameNeutralVolume = client.options.getSoundVolumeOption(SoundCategory.NEUTRAL).getValue();

        // Kiểm tra xem có thay đổi không
        boolean hasChanged = (currentGameHostileVolume != lastKnownHostileVolume ||
                             currentGameNeutralVolume != lastKnownNeutralVolume);

        if (hasChanged) {
            // Cập nhật giá trị cuối cùng
            lastKnownHostileVolume = currentGameHostileVolume;
            lastKnownNeutralVolume = currentGameNeutralVolume;

            // Phân tích trạng thái mới dựa trên giá trị âm thanh
            boolean newSoundState = !(currentGameHostileVolume == 0.0 && currentGameNeutralVolume == 0.0);

            if (newSoundState != isMobSoundOn) {
                // Trạng thái đã thay đổi, cập nhật
                isMobSoundOn = newSoundState;

                if (isMobSoundOn) {
                    // Người dùng đã bật âm thanh, lưu giá trị hiện tại
                    currentHostileVolume = currentGameHostileVolume;
                    currentNeutralVolume = currentGameNeutralVolume;
                }

                if (ModConfig.DEBUG_MODE) {
                    System.out.println("[" + ModConfig.MOD_NAME + "] User changed sound settings - New state: " +
                        (isMobSoundOn ? "ON" : "OFF") + ", Volumes: Hostile=" + currentGameHostileVolume +
                        ", Neutral=" + currentGameNeutralVolume);
                }
            } else if (isMobSoundOn) {
                // Trạng thái không đổi nhưng đang bật, cập nhật giá trị hiện tại
                currentHostileVolume = currentGameHostileVolume;
                currentNeutralVolume = currentGameNeutralVolume;

                if (ModConfig.DEBUG_MODE) {
                    System.out.println("[" + ModConfig.MOD_NAME + "] User adjusted sound volumes - Hostile: " +
                        currentHostileVolume + ", Neutral: " + currentNeutralVolume);
                }
            }
        }
    }



    /**
     * Đặt âm lượng cho hostile mobs
     */
    private void setHostileVolume(MinecraftClient client, double volume) {
        client.options.getSoundVolumeOption(SoundCategory.HOSTILE).setValue(volume);
    }

    /**
     * Đặt âm lượng cho neutral/friendly mobs
     */
    private void setNeutralVolume(MinecraftClient client, double volume) {
        client.options.getSoundVolumeOption(SoundCategory.NEUTRAL).setValue(volume);
    }

    /**
     * Hiển thị thông báo trạng thái hiện tại
     */
    private void showStateMessage(MinecraftClient client, boolean isOn) {
        if (client.player != null) {
            Text featureName = Text.translatable(TranslationKeys.MOB_SOUND_NAME);
            Text statusText = Text.translatable(isOn ? TranslationKeys.MESSAGE_ON : TranslationKeys.MESSAGE_OFF);
            String color = isOn ? "§a" : "§c"; // Green for ON, Red for OFF

            client.player.sendMessage(
                Text.literal(featureName.getString() + "§7: " + color + statusText.getString()),
                true
            );
        }
    }

    /**
     * Reset mob sound về giá trị gốc ban đầu của Minecraft (dùng khi thoát game)
     */
    public void resetMobSound(MinecraftClient client) {
        if (client != null && client.options != null) {
            // Hoàn nguyên về giá trị gốc ban đầu của Minecraft
            setHostileVolume(client, minecraftOriginalHostileVolume);
            setNeutralVolume(client, minecraftOriginalNeutralVolume);
            isMobSoundOn = true;

            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Mob sound reset to Minecraft original volumes - Hostile: " +
                    minecraftOriginalHostileVolume + ", Neutral: " + minecraftOriginalNeutralVolume);
            }
        }
    }

    // Getters

    public String getFeatureName() {
        return TranslationKeys.MOB_SOUND_FEATURE;
    }

    public KeyBinding getToggleKey() {
        return toggleKey;
    }

    public boolean isEnabled() {
        return ModConfig.ENABLE_MOB_SOUND;
    }
}
