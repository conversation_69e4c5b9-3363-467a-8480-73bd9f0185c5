package net.aethor.features;

import net.aethor.config.ModConfig;
import net.aethor.utils.TranslationKeys;
import net.minecraft.sound.SoundCategory;

/**
 * Feature để tắt bật âm thanh nhạc nền bằng phím tắt
 * Có 2 trạng thái: ON/OFF cho music sound
 * - ON: Nhạc nền có âm thanh
 * - OFF: Tắt âm thanh nhạc nền
 */
public class MusicSoundFeature extends AbstractSoundFeature {

    public MusicSoundFeature() {
        super();
    }

    /**
     * Khởi tạo và lưu giá trị gốc ban đầu của Minecraft
     * Gọi method này sau khi client đã sẵn sàng (chỉ một lần)
     */
    public void initialize(MinecraftClient client) {
        if (!isInitialized && client != null && client.options != null) {
            // Lưu giá trị gốc ban đầu của Minecraft
            minecraftOriginalMusicVolume = client.options.getSoundVolumeOption(SoundCategory.MUSIC).getValue();
            
            // Kiểm tra trạng thái ban đầu: nếu = 0 thì đang tắt
            boolean isOriginallyOff = (minecraftOriginalMusicVolume == 0.0);
            
            if (isOriginallyOff) {
                // Nếu ban đầu đã tắt, set trạng thái là OFF và giá trị hiện tại là default restore volume để restore
                isMusicSoundOn = false;
                currentMusicVolume = ModConfig.MUSIC_SOUND_DEFAULT_RESTORE_VOLUME;
            } else {
                // Nếu ban đầu đang bật, set trạng thái là ON và giá trị hiện tại bằng giá trị gốc
                isMusicSoundOn = true;
                currentMusicVolume = minecraftOriginalMusicVolume;
            }
            
            // Khởi tạo giá trị cuối cùng để theo dõi thay đổi
            lastKnownMusicVolume = minecraftOriginalMusicVolume;
            
            isInitialized = true;

            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Initialized Music Sound - Original volume: " + 
                    minecraftOriginalMusicVolume + ", Initial state: " + (isMusicSoundOn ? "ON" : "OFF") + 
                    ", Default restore volume: " + ModConfig.MUSIC_SOUND_DEFAULT_RESTORE_VOLUME);
            }
        }
    }

    /**
     * Xử lý input từ bàn phím cho music sound feature
     */
    public void handleInput(MinecraftClient client) {
        if (client.player == null) return;

        // Theo dõi thay đổi settings âm thanh từ người dùng
        checkForSoundSettingsChanges(client);

        // Xử lý phím toggle
        while (toggleKey.wasPressed()) {
            toggleMusicSound(client);
        }
    }

    /**
     * Theo dõi thay đổi settings âm thanh từ người dùng
     */
    private void checkForSoundSettingsChanges(MinecraftClient client) {
        if (!isInitialized || client == null || client.options == null) return;

        double currentGameMusicVolume = client.options.getSoundVolumeOption(SoundCategory.MUSIC).getValue();

        // Kiểm tra xem có thay đổi không
        boolean hasChanged = (currentGameMusicVolume != lastKnownMusicVolume);

        if (hasChanged) {
            // Cập nhật giá trị cuối cùng
            lastKnownMusicVolume = currentGameMusicVolume;

            // Phân tích trạng thái mới dựa trên giá trị âm thanh
            boolean newSoundState = !(currentGameMusicVolume == 0.0);
            
            if (newSoundState != isMusicSoundOn) {
                // Trạng thái đã thay đổi, cập nhật
                isMusicSoundOn = newSoundState;
                
                if (isMusicSoundOn) {
                    // Người dùng đã bật âm thanh, lưu giá trị hiện tại
                    currentMusicVolume = currentGameMusicVolume;
                }

                if (ModConfig.DEBUG_MODE) {
                    System.out.println("[" + ModConfig.MOD_NAME + "] User changed music settings - New state: " + 
                        (isMusicSoundOn ? "ON" : "OFF") + ", Volume: " + currentGameMusicVolume);
                }
            } else if (isMusicSoundOn) {
                // Trạng thái không đổi nhưng đang bật, cập nhật giá trị hiện tại
                currentMusicVolume = currentGameMusicVolume;

                if (ModConfig.DEBUG_MODE) {
                    System.out.println("[" + ModConfig.MOD_NAME + "] User adjusted music volume: " + currentMusicVolume);
                }
            }
        }
    }

    /**
     * Toggle giữa ON/OFF music sound
     */
    private void toggleMusicSound(MinecraftClient client) {
        // Chuyển đổi trạng thái
        isMusicSoundOn = !isMusicSoundOn;
        
        if (isMusicSoundOn) {
            // Bật âm thanh - restore âm lượng hiện tại
            setMusicVolume(client, currentMusicVolume);
            showStateMessage(client, true);
        } else {
            // Lưu âm lượng hiện tại trước khi tắt (chỉ khi không phải trường hợp đặc biệt)
            if (!(minecraftOriginalMusicVolume == 0.0)) {
                currentMusicVolume = client.options.getSoundVolumeOption(SoundCategory.MUSIC).getValue();
            }
            // Tắt âm thanh - set về 0
            setMusicVolume(client, 0.0);
            showStateMessage(client, false);
        }

        if (ModConfig.DEBUG_MODE) {
            System.out.println("[" + ModConfig.MOD_NAME + "] Music sound state changed to: " + (isMusicSoundOn ? "ON" : "OFF") +
                " (Current volume: " + currentMusicVolume + ")");
        }
    }

    /**
     * Đặt âm lượng cho music
     */
    private void setMusicVolume(MinecraftClient client, double volume) {
        client.options.getSoundVolumeOption(SoundCategory.MUSIC).setValue(volume);
    }

    /**
     * Hiển thị thông báo trạng thái hiện tại
     */
    private void showStateMessage(MinecraftClient client, boolean isOn) {
        if (client.player != null) {
            Text featureName = Text.translatable(TranslationKeys.MUSIC_SOUND_NAME);
            Text statusText = Text.translatable(isOn ? TranslationKeys.MESSAGE_ON : TranslationKeys.MESSAGE_OFF);
            String color = isOn ? "§a" : "§c"; // Green for ON, Red for OFF

            client.player.sendMessage(
                Text.literal(featureName.getString() + "§7: " + color + statusText.getString()),
                true
            );
        }
    }

    /**
     * Reset music sound về giá trị gốc ban đầu của Minecraft (dùng khi thoát game)
     */
    public void resetMusicSound(MinecraftClient client) {
        if (client != null && client.options != null) {
            // Hoàn nguyên về giá trị gốc ban đầu của Minecraft
            setMusicVolume(client, minecraftOriginalMusicVolume);
            isMusicSoundOn = true;
            
            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Music sound reset to Minecraft original volume: " + 
                    minecraftOriginalMusicVolume);
            }
        }
    }

    // Getters

    public String getFeatureName() {
        return TranslationKeys.MUSIC_SOUND_FEATURE;
    }

    public KeyBinding getToggleKey() {
        return toggleKey;
    }

    public boolean isEnabled() {
        return ModConfig.ENABLE_MUSIC_SOUND;
    }
}
