package net.aethor.features;

import net.aethor.config.ModConfig;
import net.aethor.utils.TranslationKeys;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import net.minecraft.text.Text;

/**
 * Feature để tăng/giảm độ sáng (brightness/gamma) bằng phím tắt
 * Cho phép điều chỉnh từ 0% (tối nhất) đến 1600% (sáng nhất)
 * Minecraft mặc định slider: 0% - 150% (gamma 0.0 - 1.5)
 * Mod này mở rộng lên đến 1600% (gamma 16.0)
 */
public class BrightnessFeature {

    private final KeyBinding increaseBrightnessKey;
    private final KeyBinding decreaseBrightnessKey;

    public BrightnessFeature() {
        // Đăng ký key bindings
        this.increaseBrightnessKey = KeyBindingHelper.registerKeyBinding(new KeyBinding(
                TranslationKeys.INCREASE_BRIGHTNESS,
                InputUtil.Type.KEYSYM,
                InputUtil.UNKNOWN_KEY.getCode(), // Không có phím mặc định
                TranslationKeys.QUICK_SETTING_CATEGORY
        ));

        this.decreaseBrightnessKey = KeyBindingHelper.registerKeyBinding(new KeyBinding(
                TranslationKeys.DECREASE_BRIGHTNESS,
                InputUtil.Type.KEYSYM,
                InputUtil.UNKNOWN_KEY.getCode(), // Không có phím mặc định
                TranslationKeys.QUICK_SETTING_CATEGORY
        ));
    }

    /**
     * Xử lý input từ bàn phím cho brightness feature
     */
    public void handleInput(MinecraftClient client) {
        if (client.player == null) return;

        // Xử lý phím tăng brightness
        while (increaseBrightnessKey.wasPressed()) {
            increaseBrightness(client);
        }

        // Xử lý phím giảm brightness
        while (decreaseBrightnessKey.wasPressed()) {
            decreaseBrightness(client);
        }
    }

    /**
     * Tăng brightness
     */
    private void increaseBrightness(MinecraftClient client) {
        double currentBrightness = client.options.getGamma().getValue();
        double newBrightness = Math.min(currentBrightness + ModConfig.BRIGHTNESS_STEP, ModConfig.BRIGHTNESS_MAX);

        if (newBrightness != currentBrightness) {
            client.options.getGamma().setValue(newBrightness);
            showBrightnessMessage(client, newBrightness, TranslationKeys.BRIGHTNESS_CHANGED);

            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Brightness increased to: " + 
                    String.format("%.1f", newBrightness) + " (" + String.format("%.0f", newBrightness * 100) + "%)");
            }
        } else {
            // Đã đạt giá trị tối đa
            showBrightnessMessage(client, newBrightness, TranslationKeys.BRIGHTNESS_MAX);

            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Brightness already at maximum: " + 
                    String.format("%.1f", newBrightness) + " (" + String.format("%.0f", newBrightness * 100) + "%)");
            }
        }
    }

    /**
     * Giảm brightness
     */
    private void decreaseBrightness(MinecraftClient client) {
        double currentBrightness = client.options.getGamma().getValue();
        double newBrightness = Math.max(currentBrightness - ModConfig.BRIGHTNESS_STEP, ModConfig.BRIGHTNESS_MIN);

        if (newBrightness != currentBrightness) {
            client.options.getGamma().setValue(newBrightness);
            showBrightnessMessage(client, newBrightness, TranslationKeys.BRIGHTNESS_CHANGED);

            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Brightness decreased to: " + 
                    String.format("%.1f", newBrightness) + " (" + String.format("%.0f", newBrightness * 100) + "%)");
            }
        } else {
            // Đã đạt giá trị tối thiểu
            showBrightnessMessage(client, newBrightness, TranslationKeys.BRIGHTNESS_MIN);

            if (ModConfig.DEBUG_MODE) {
                System.out.println("[" + ModConfig.MOD_NAME + "] Brightness already at minimum: " + 
                    String.format("%.1f", newBrightness) + " (" + String.format("%.0f", newBrightness * 100) + "%)");
            }
        }
    }

    /**
     * Hiển thị thông báo brightness hiện tại
     */
    private void showBrightnessMessage(MinecraftClient client, double brightness, String messageType) {
        if (client.player != null) {
            Text featureName = Text.translatable(TranslationKeys.BRIGHTNESS_NAME);
            
            // Chuyển đổi brightness thành phần trăm để hiển thị
            int brightnessPercent = (int) Math.round(brightness * 100);
            
            // Tạo thông báo với màu sắc tương ứng
            String color = getColorForBrightness(brightness);
            String message = featureName.getString() + "§7: " + color + brightnessPercent + "%";
            
            client.player.sendMessage(Text.literal(message), true);
        }
    }

    /**
     * Lấy màu sắc tương ứng với mức brightness
     */
    private String getColorForBrightness(double brightness) {
        if (brightness <= 0.0) {
            return "§8"; // Dark Gray - rất tối
        } else if (brightness <= 0.5) {
            return "§7"; // Gray - tối
        } else if (brightness <= 1.0) {
            return "§f"; // White - bình thường
        } else if (brightness <= 5.0) {
            return "§e"; // Yellow - sáng
        } else if (brightness <= 10.0) {
            return "§6"; // Gold - rất sáng
        } else {
            return "§c"; // Red - cực sáng (trên 1000%)
        }
    }

    // Getters

    public String getFeatureName() {
        return TranslationKeys.BRIGHTNESS_FEATURE;
    }

    public KeyBinding getIncreaseBrightnessKey() {
        return increaseBrightnessKey;
    }

    public KeyBinding getDecreaseBrightnessKey() {
        return decreaseBrightnessKey;
    }

    public boolean isEnabled() {
        return ModConfig.ENABLE_BRIGHTNESS;
    }

    /**
     * Lấy brightness hiện tại
     */
    public double getCurrentBrightness(MinecraftClient client) {
        return client.options.getGamma().getValue();
    }

    /**
     * Lấy brightness hiện tại dưới dạng phần trăm
     */
    public int getCurrentBrightnessPercent(MinecraftClient client) {
        return (int) Math.round(getCurrentBrightness(client) * 100);
    }
}
