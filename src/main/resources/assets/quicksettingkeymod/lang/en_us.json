{"category.quicksettingkeymod.quick_settings": "QuickSettingKey", "key.quicksettingkeymod.increase_render_distance": "Render Distance Increase", "key.quicksettingkeymod.decrease_render_distance": "Render Distance Decrease", "key.quicksettingkeymod.increase_simulation_distance": "Simulation Distance Increase", "key.quicksettingkeymod.decrease_simulation_distance": "Simulation Distance Decrease", "key.quicksettingkeymod.increase_fov": "FOV Increase", "key.quicksettingkeymod.decrease_fov": "FOV Decrease", "key.quicksettingkeymod.zoom": "Zoom (Hold)", "key.quicksettingkeymod.toggle_hostile_mob_sound": "Toggle Hostile Mob Sound", "key.quicksettingkeymod.toggle_friendly_mob_sound": "Toggle Friendly Mob Sound", "key.quicksettingkeymod.toggle_music_sound": "Toggle Music Sound", "key.quicksettingkeymod.toggle_jukebox_sound": "Toggle Jukebox Sound", "message.quicksettingkeymod.render_distance_name": "Render Distance", "message.quicksettingkeymod.simulation_distance_name": "Simulation Distance", "message.quicksettingkeymod.fov_name": "FOV", "message.quicksettingkeymod.hostile_mob_sound_name": "Hostile Mob Sound", "message.quicksettingkeymod.friendly_mob_sound_name": "Friendly Mob Sound", "message.quicksettingkeymod.music_sound_name": "Music Sound", "message.quicksettingkeymod.on": "ON", "message.quicksettingkeymod.off": "OFF", "message.quicksettingkeymod.message_max": "Maximum", "message.quicksettingkeymod.message_min": "Minimum", "message.quicksettingkeymod.render_distance_changed": "§a%s§7: §e%d §7chunks", "message.quicksettingkeymod.render_distance_max": "§c%s %s §e%d §7chunks", "message.quicksettingkeymod.render_distance_min": "§c%s %s §e%d §7chunks", "message.quicksettingkeymod.simulation_distance_changed": "§a%s§7: §e%d §7chunks", "message.quicksettingkeymod.simulation_distance_max": "§c%s %s §e%d §7chunks", "message.quicksettingkeymod.simulation_distance_min": "§c%s %s §e%d §7chunks", "message.quicksettingkeymod.fov_changed": "§a%s§7: §e%d°", "message.quicksettingkeymod.fov_max": "§c%s %s §e%d°", "message.quicksettingkeymod.fov_min": "§c%s %s §e%d°", "message.quicksettingkeymod.distance_preview": "§7Preview: §f%s §7= §e%d §7(applying in §c%d§7s...)"}