# QuickSettingKeyMod

<div align="center">

![Minecraft](https://img.shields.io/badge/Minecraft-1.21.6-green.svg)
![Fabric](https://img.shields.io/badge/Fabric-0.16.14+-blue.svg)
![Java](https://img.shields.io/badge/Java-21+-orange.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)

**Mod Fabric cho phép điều chỉnh Render Distance và Simulation Distance bằng phím tắt trong Minecraft**

[📥 Download](#-installation) • [🎮 Sử dụng](#-usage) • [🔧 Cấu hình](#-configuration) • [🤝 Đóng góp](#-contributing)

</div>

---

## 📋 Mục lục

- [✨ Tính năng](#-tính-năng)
- [📦 Cài đặt](#-cài-đặt)
- [🎮 Sử dụng](#-sử-dụng)
- [🔧 Cấu hình](#-cấu-hình)
- [🏗️ Cấu trúc dự án](#️-cấu-trúc-dự-án)
- [🛠️ Development](#️-development)
- [🤝 Đóng góp](#-đóng-góp)
- [📄 License](#-license)

---

## ✨ Tính năng

### 🎯 Core Features
- **⚡ Điều chỉnh nhanh Render Distance**: Sử dụng phím `=` (tăng) và `-` (giảm)
- **🔄 Điều chỉnh Simulation Distance**: Phím tắt tùy chỉnh (không có phím mặc định)
- **📱 Thông báo trực quan**: Hiển thị thay đổi trên Action Bar
- **🌍 Đa ngôn ngữ**: Hỗ trợ tiếng Anh và tiếng Việt
- **⚙️ Cấu hình linh hoạt**: Tùy chỉnh giới hạn và phím tắt

### 🚀 Advanced Features
- **🏗️ Kiến trúc modular**: Dễ dàng mở rộng thêm tính năng mới
- **🎛️ Performance monitoring**: Theo dõi impact của distance settings
- **🔒 Safe limits**: Giới hạn an toàn để tránh lag game
- **🐛 Debug mode**: Hỗ trợ debugging và troubleshooting
- **💾 Message throttling**: Tránh spam thông báo

---

## 📦 Cài đặt

### Yêu cầu hệ thống
- **Minecraft**: 1.21.6
- **Fabric Loader**: 0.16.14+
- **Fabric API**: 0.115.10+1.21.6
- **Java**: 21+

### Các bước cài đặt
1. **Tải và cài đặt Fabric Loader** từ [fabricmc.net](https://fabricmc.net/)
2. **Tải Fabric API** từ [Modrinth](https://modrinth.com/mod/fabric-api) hoặc [CurseForge](https://www.curseforge.com/minecraft/mc-mods/fabric-api)
3. **Tải QuickSettingKeyMod** từ [Releases](https://github.com/yourusername/QuickSettingKeyMod/releases)
4. **Đặt các file `.jar`** vào thư mục `mods/` trong Minecraft directory
5. **Khởi động Minecraft** với profile Fabric

### 📁 Cấu trúc thư mục
```
.minecraft/
├── mods/
│   ├── fabric-api-0.115.10+1.21.6.jar
│   └── quicksettingkeymod-1.0.0.jar
└── ...
```

---

## 🎮 Sử dụng

### 🎹 Phím tắt mặc định

#### Render Distance
- **`=` (Equal)**: Tăng render distance lên 1 chunk
- **`-` (Minus)**: Giảm render distance xuống 1 chunk

#### Simulation Distance
- **Không có phím mặc định**: Cần tự cài đặt trong Controls

### ⚙️ Cài đặt phím tắt
1. Mở **Options** → **Controls** → **Key Binds**
2. Tìm categories:
    - **"Render Distance Controls"**: Cho render distance
    - **"Simulation Distance Controls"**: Cho simulation distance
3. Click vào phím muốn thay đổi và nhấn phím mới
4. **Lưu settings** và sử dụng

### 📊 Giới hạn distance
| Setting | Tối thiểu | Tối đa | Mặc định |
|---------|-----------|-------|----------|
| **Render Distance** | 2 chunks | 32 chunks | 12 chunks |
| **Simulation Distance** | 5 chunks | 32 chunks | 8 chunks |

### 💡 Tips sử dụng
- **Performance**: Render distance cao ảnh hưởng FPS, simulation distance ảnh hưởng TPS
- **Balanced settings**: 12 render + 8 simulation cho hầu hết máy
- **High-end systems**: Có thể tăng lên 16-24 render distance
- **Low-end systems**: Nên giữ ở 6-8 render distance

---

## 🔧 Cấu hình

### 🎛️ Config Options

Các setting có thể điều chỉnh trong `ModConfig.java`:

```java
// Distance Limits
public static final int MIN_RENDER_DISTANCE = 2;
public static final int MAX_RENDER_DISTANCE = 32;
public static final int MIN_SIMULATION_DISTANCE = 5;
public static final int MAX_SIMULATION_DISTANCE = 32;

// Message Settings
public static final boolean SHOW_ACTION_BAR_MESSAGES = true;
public static final int MESSAGE_DISPLAY_DURATION = 3000; // ms

// Debug Settings
public static final boolean DEBUG_MODE = false;
public static final boolean LOG_KEY_PRESSES = false;
```

### 🌍 Thêm ngôn ngữ mới

Để thêm ngôn ngữ mới, tạo file trong `assets/quicksettingkeymod/lang/`:

```json
// assets/quicksettingkeymod/lang/your_language.json
{
  "key.quicksettingkeymod.increase_render_distance": "Your Translation",
  "key.quicksettingkeymod.decrease_render_distance": "Your Translation",
  // ... other keys
}
```

---

## 🏗️ Cấu trúc dự án

### 📂 Source Code Structure
```
src/main/java/net/aethor/
├── QuickSettingKeyMod.java          # 🚀 Main mod loader
├── config/                          # ⚙️ Configuration layer
│   ├── ModConfig.java              #    Global settings
│   └── KeyBindingConfig.java       #    Keybinding configs
├── handlers/                        # 🎮 Event handlers
│   ├── KeyInputHandler.java        #    Input processing
│   └── MessageHandler.java         #    Message display
├── features/                        # ✨ Feature implementations
│   ├── AbstractDistanceFeature.java #    Base feature class
│   ├── RenderDistanceFeature.java  #    Render distance logic
│   └── SimulationDistanceFeature.java #  Simulation distance logic
└── utils/                          # 🛠️ Utilities
    ├── DistanceUtils.java          #    Distance calculations
    └── TranslationKeys.java        #    Translation key management
```

### 🎯 Design Principles
- **🔧 Modular Architecture**: Mỗi component có trách nhiệm riêng biệt
- **🔄 Plugin System**: Dễ dàng thêm features mới
- **⚡ Performance First**: Tối ưu cho performance và memory
- **🧪 Testable**: Structure cho phép unit testing
- **📈 Scalable**: Có thể mở rộng cho nhiều settings khác

---

## 🛠️ Development

### 🏃 Quick Start
```bash
# Clone repository
git clone https://github.com/yourusername/QuickSettingKeyMod.git
cd QuickSettingKeyMod

# Build mod
./gradlew build

# Run development environment
./gradlew runClient
```

### 📋 Development Requirements
- **JDK 21+**
- **Gradle 8.0+**
- **IntelliJ IDEA** hoặc **Visual Studio Code** (recommended)

### 🔨 Build Commands
```bash
# Clean build
./gradlew clean build

# Build without tests
./gradlew build -x test

# Generate sources jar
./gradlew build publishToMavenLocal
```

### 🧪 Testing
```bash
# Run all tests
./gradlew test

# Run specific test
./gradlew test --tests "DistanceUtilsTest"

# Run with coverage
./gradlew test jacocoTestReport
```

### 📦 Adding New Features

Để thêm feature mới (ví dụ: FOV Control):

1. **Tạo class mới** extend `AbstractDistanceFeature`:
```java
public class FOVControlFeature extends AbstractDistanceFeature {
    // Implementation
}
```

2. **Thêm translation keys** vào `TranslationKeys.java`
3. **Register feature** trong `QuickSettingKeyMod.java`
4. **Update language files** với translations mới

---

## 🤝 Đóng góp

Chúng tôi luôn chào đón contributions!

### 🎯 Cách đóng góp
1. **🍴 Fork** repository này
2. **🌿 Tạo feature branch**: `git checkout -b feature/amazing-feature`
3. **💾 Commit changes**: `git commit -m 'Add amazing feature'`
4. **📤 Push**: `git push origin feature/amazing-feature`
5. **🔄 Tạo Pull Request**

### 📋 Contribution Guidelines
- **✅ Code style**: Follow existing code conventions
- **📝 Documentation**: Update README và comments khi cần
- **🧪 Testing**: Thêm tests cho features mới
- **🐛 Bug reports**: Sử dụng issue templates
- **💡 Feature requests**: Mô tả chi tiết use case

### 🐛 Bug Reports
Khi báo lỗi, vui lòng bao gồm:
- **Minecraft version**
- **Fabric Loader version**
- **Mod version**
- **Steps to reproduce**
- **Expected behavior**
- **Screenshots/logs** (nếu có)

---

## 📊 Performance Notes

### 🎮 Render Distance Impact
| Distance | Chunks Loaded | Memory Usage | Performance Impact |
|----------|---------------|--------------|-------------------|
| 2-6      | 25-169        | Low          | ✅ Minimal        |
| 8-12     | 289-625       | Medium       | ⚠️ Moderate       |
| 16-24    | 1089-2401     | High         | ❌ Significant    |
| 28-32    | 3249-4225     | Very High    | 🚫 Extreme        |

### ⚡ Optimization Tips
- **Balanced setup**: 12 render + 8 simulation distance
- **Performance mode**: 6 render + 5 simulation distance
- **Quality mode**: 16 render + 12 simulation distance
- **Monitor FPS**: Giảm distance nếu FPS < 30

---

## 🗺️ Roadmap

### 🚀 Version 1.1.0
- [ ] **🎛️ GUI Configuration**: In-game config screen
- [ ] **📊 Performance Monitor**: Real-time FPS/TPS display
- [ ] **⚡ Auto-adjustment**: Dynamic distance based on performance
- [ ] **🔧 More settings**: FOV, brightness, gamma controls

### 🌟 Version 1.2.0
- [ ] **🌐 Multiplayer optimization**: Server-aware settings
- [ ] **📱 Mobile-friendly**: Touch controls for mobile Minecraft
- [ ] **🎨 Custom UI**: Better visual feedback
- [ ] **🔄 Profiles**: Preset configurations for different scenarios

### 🔮 Future Ideas
- **🤖 AI-powered optimization**: Machine learning cho optimal settings
- **🌍 Biome-aware**: Auto-adjust based on current biome
- **👥 Multiplayer sync**: Share settings với friends
- **📈 Analytics**: Performance tracking và insights

---

## 🙏 Credits

### 👨‍💻 Development Team
- **LuyenMinecraft** - *Main Developer* - [GitHub](https://github.com/dinhluyen93)

### 🤝 Special Thanks
- **Fabric Team** - Framework và documentation tuyệt vời
- **Minecraft Community** - Feedback và bug reports
- **Beta Testers** - Giúp test và improve mod

### 📚 Libraries & Tools
- **Fabric API** - Core mod framework
- **Fabric Loader** - Mod loading system
- **Gradle** - Build automation
- **IntelliJ IDEA** - Development environment

---

## 📄 License

Dự án này được license dưới [MIT License](LICENSE).

<div align="center">

**⭐ Nếu bạn thích mod này, hãy star repository! ⭐**

[🐛 Report Bug](https://github.com/dinhluyen93/QuickSettingKeyMod/issues) • [💡 Request Feature](https://github.com/dinhluyen93/QuickSettingKeyMod/issues) • [💬 Discord](https://discord.gg/yourdiscord)

**Made with ❤️ for the Minecraft community**

</div>